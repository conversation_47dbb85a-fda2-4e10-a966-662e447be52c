"""
SQLAlchemy model for Pinnacle Prescription data.

This module provides the Prescription model for representing treatment prescription
details within a Pinnacle trial. It includes dose targets, fractionation schemes,
and normalization parameters.
"""

from __future__ import annotations
from typing import Optional, TYPE_CHECKING

from sqlalchemy import <PERSON>umn, <PERSON>, Integer, Float, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.trial import Trial


class Prescription(PinnacleBase):
    """
    Model representing a radiation therapy prescription within a Pinnacle treatment trial.

    This class stores comprehensive prescription information that defines the therapeutic
    intent and dose delivery parameters for radiation therapy treatment. Prescriptions
    are fundamental components of treatment planning that specify the total dose,
    fractionation schedule, normalization methods, and quality assurance parameters
    required for safe and effective radiation therapy delivery.

    In Pinnacle, a trial can contain multiple prescriptions to accommodate complex
    treatment scenarios such as sequential boost treatments, simultaneous integrated
    boosts (SIB), or multi-phase treatment protocols. Each prescription defines specific
    dose objectives and constraints that guide treatment planning optimization and
    delivery verification.

    Technical Details:
        - Supports multiple normalization methods (point-based, isodose-based, maximum dose)
        - <PERSON>les uncertainty analysis for dose delivery validation
        - Integrates with monitor unit calculations for treatment delivery
        - Provides color coding for visual identification in treatment planning
        - Supports complex fractionation schemes and dose escalation protocols

    Clinical Applications:
        - Primary target volume prescriptions
        - Boost volume prescriptions for dose escalation
        - Simultaneous integrated boost (SIB) treatments
        - Sequential treatment phases
        - Adaptive radiation therapy protocols
        - Stereotactic treatments with high precision requirements

    Use Cases:
        - Treatment plan optimization and evaluation
        - Dose-volume histogram (DVH) analysis
        - Monitor unit calculation and verification
        - Treatment delivery quality assurance
        - Clinical protocol compliance verification
        - Multi-institutional treatment standardization

    Attributes:
        # Core Identification
        id (int): Primary key identifier for the prescription
        name (str): Human-readable prescription name (e.g., "PTV_70Gy", "Boost_Phase_II")
        color (str): Color code for visual identification in treatment planning interface

        # Dose Specification
        prescription_dose (float): Total prescribed dose in Gray (Gy)
        number_of_fractions (int): Number of treatment fractions for dose delivery
        prescription_percent (int): Isodose line percentage for dose normalization (e.g., 95%)
        prescription_point (str): Point of Interest (POI) name used for dose prescription

        # Normalization and Calculation Methods
        normalization_method (str): Dose normalization method specification
            - "MaxDose": Normalize to maximum dose in target
            - "ToPoint": Normalize to specific prescription point
            - "MeanDose": Normalize to mean target dose
            - "MedianDose": Normalize to median target dose
        method (str): Prescription calculation method
        prescription_period (str): Treatment period specification for fractionation
        weights_proportional_to (str): Beam weight proportionality method

        # Monitor Unit and Delivery Parameters
        requested_monitor_units_per_fraction (int): Target monitor units per fraction

        # Uncertainty Analysis and Quality Assurance
        dose_uncertainty (int): Dose calculation uncertainty percentage
        prescription_uncertainty (int): Prescription delivery uncertainty percentage
        dose_uncertainty_valid (int): Flag indicating dose uncertainty validity (0/1)
        prescrip_uncertainty_valid (int): Flag indicating prescription uncertainty validity (0/1)

        # Parent Relationship
        trial_id (int): Foreign key to the parent Trial

    Relationships:
        trial (Trial): Parent treatment trial that contains this prescription (many-to-one)
            - Back reference: trial.prescription_list
            - Lazy loading: joined (optimized for frequent access)
            - Use for: Treatment planning context and multi-prescription coordination

    Example:
        >>> # Create primary prescription for prostate treatment
        >>> primary_rx = Prescription(
        ...     name="PTV_Prostate_78Gy",
        ...     prescription_dose=78.0,
        ...     number_of_fractions=39,
        ...     prescription_percent=95,
        ...     prescription_point="Isocenter",
        ...     normalization_method="ToPoint",
        ...     color="Red"
        ... )
        >>>
        >>> # Create boost prescription for SIB treatment
        >>> boost_rx = Prescription(
        ...     name="PTV_Boost_84Gy",
        ...     prescription_dose=84.0,
        ...     number_of_fractions=28,
        ...     prescription_percent=98,
        ...     normalization_method="MeanDose",
        ...     color="Blue"
        ... )
        >>>
        >>> # Calculate dose per fraction
        >>> dose_per_fx = primary_rx.prescription_dose / primary_rx.number_of_fractions
        >>> print(f"Dose per fraction: {dose_per_fx:.2f} Gy")
    """

    __tablename__ = "Prescription"

    # Primary key is inherited from PinnacleBase
    name: Mapped[Optional[str]] = Column("Name", String, nullable=True)
    requested_monitor_units_per_fraction: Mapped[Optional[int]] = Column(
        "RequestedMonitorUnitsPerFraction", Integer, nullable=True
    )
    prescription_dose: Mapped[Optional[float]] = Column("PrescriptionDose", Float, nullable=True)
    prescription_percent: Mapped[Optional[int]] = Column(
        "PrescriptionPercent", Integer, nullable=True
    )
    number_of_fractions: Mapped[Optional[int]] = Column("NumberOfFractions", Integer, nullable=True)
    prescription_point: Mapped[Optional[str]] = Column("PrescriptionPoint", String, nullable=True)
    method: Mapped[Optional[str]] = Column("Method", String, nullable=True)
    normalization_method: Mapped[Optional[str]] = Column(
        "NormalizationMethod", String, nullable=True
    )
    prescription_period: Mapped[Optional[str]] = Column("PrescriptionPeriod", String, nullable=True)
    weights_proportional_to: Mapped[Optional[str]] = Column(
        "WeightsProportionalTo", String, nullable=True
    )
    dose_uncertainty: Mapped[Optional[int]] = Column("DoseUncertainty", Integer, nullable=True)
    prescription_uncertainty: Mapped[Optional[int]] = Column(
        "PrescriptionUncertainty", Integer, nullable=True
    )
    dose_uncertainty_valid: Mapped[Optional[int]] = Column(
        "DoseUncertaintyValid", Integer, nullable=True
    )
    prescrip_uncertainty_valid: Mapped[Optional[int]] = Column(
        "PrescripUncertaintyValid", Integer, nullable=True
    )
    color: Mapped[Optional[str]] = Column("Color", String, nullable=True)

    # Foreign key to the parent Trial
    trial_id: Mapped[int] = Column("TrialID", Integer, ForeignKey("Trial.ID"), nullable=False)

    # Parent relationship, eagerly loaded for performance.
    trial: Mapped["Trial"] = relationship(
        "Trial",
        back_populates="prescription_list",
        lazy="joined"
    )

    def __repr__(self) -> str:
        """
        Provide a developer-friendly string representation of the Prescription.
        """
        return (
            f"<Prescription(id={self.id}, name='{self.name}', "
            f"dose={self.prescription_dose}, fx={self.number_of_fractions})>"
        )
